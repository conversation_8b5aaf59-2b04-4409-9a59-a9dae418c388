package com.glodon.qydata.service.system.impl;

import cn.hutool.core.lang.Pair;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.controller.temp.areaRepair.AreaDiff;
import com.glodon.qydata.controller.temp.areaRepair.AreaDiffResult;
import com.glodon.qydata.entity.system.TbArea;
import com.glodon.qydata.entity.system.TbAreaChangeRecord;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.system.TbAreaChangeRecordMapper;
import com.glodon.qydata.mapper.system.TbAreaMapper;
import com.glodon.qydata.service.system.IAreaService;
import com.glodon.qydata.util.AMapApiUtil;
import com.glodon.qydata.util.AreaUtil;
import com.glodon.qydata.util.MethodAnnotationUtil;
import com.glodon.qydata.util.PinyinUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.vo.amap.AMapDistrict;
import com.glodon.qydata.vo.system.UpdateAreaVO;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
@Slf4j
public class AreaServiceImpl implements IAreaService {

    @Autowired
    private TbAreaMapper areaMapper;

    @Resource
    private TbAreaChangeRecordMapper areaChangeRecordMapper;

    @Autowired
    RedisUtil redisUtil;

    @PostConstruct
    private void initData(){
        // 所有地区
        List<TbArea> allArea = areaMapper.getAreaList(Constants.AREA_ALL);
        if (CollectionUtils.isNotEmpty(allArea)){
            redisUtil.setList(RedisKeyEnum.ALL_AREA, allArea, Constants.AREA_ALL);
        }

        // 有效地区
        List<TbArea> validArea = areaMapper.getAreaList(Constants.AREA_ONLY_VALID);
        if (CollectionUtils.isNotEmpty(validArea)){
            redisUtil.setList(RedisKeyEnum.ALL_AREA, validArea, Constants.AREA_ONLY_VALID);
        }
    }

    @Override
    public List<TbArea> getAreaTree(String pid, String scope){
        return listToTree(getAllArea(scope), pid);
    }

    /**
     * 获取所有地区数据
     * @return
     */
    @Override
    public List<TbArea> getAllArea(String scope){
        // 演示：获取当前请求的HTTP方法
        String httpMethod = RequestContent.getHttpMethod();
        log.info("当前请求方法: {}", httpMethod);

        // 演示：获取当前Controller方法的Operation注解
        Operation operationAnnotation = RequestContent.getCurrentMethodAnnotation(Operation.class);
        if (operationAnnotation != null) {
            log.info("当前方法的Operation注解summary: {}", operationAnnotation.summary());
        }

        // 演示：获取当前方法的所有注解
        java.lang.annotation.Annotation[] annotations = RequestContent.getCurrentMethodAnnotations();
        log.info("当前方法共有 {} 个注解", annotations.length);
        for (java.lang.annotation.Annotation annotation : annotations) {
            log.info("注解类型: {}", annotation.annotationType().getSimpleName());
        }

        // 演示：获取当前执行的方法信息
        java.lang.reflect.Method currentMethod = RequestContent.getCurrentMethod();
        if (currentMethod != null) {
            log.info("当前执行的方法: {}.{}", currentMethod.getDeclaringClass().getSimpleName(), currentMethod.getName());
        }

        List<TbArea> areaList = redisUtil.getListObject(RedisKeyEnum.ALL_AREA, TbArea.class, scope);

        if (CollectionUtils.isNotEmpty(areaList)){
            return areaList;
        }

        areaList = areaMapper.getAreaList(scope);

        if (CollectionUtils.isNotEmpty(areaList)){
            redisUtil.setList(RedisKeyEnum.ALL_AREA, areaList, scope);
        }

        return Optional.ofNullable(areaList).orElse(new ArrayList<>());
    }

    @Override
    public List<TbArea> getAreaByAreaIds(List<String> areaIds) {
        if (CollectionUtils.isEmpty(areaIds)) {
            return new ArrayList<>();
        }
        LinkedHashMap<String, TbArea> idsLinkMap = new LinkedHashMap<>();
        areaIds.forEach(id -> idsLinkMap.put(id, null));
        List<TbArea> allArea = areaMapper.getAreaByAreaIds(areaIds);
        if(allArea == null) {
            return new ArrayList<>(idsLinkMap.values());
        }
        allArea.forEach(area -> {
            if(!idsLinkMap.containsKey(area.getAreaid())) {
                return;
            }
            // 地区信息是要获取的则放进map
            idsLinkMap.put(area.getAreaid(), area);
        });
        return new ArrayList<>(idsLinkMap.values());
    }

    /**
     　　* @description: 仅限三级以内调用,如果level = 3,那么provinceOrCity为市区的值
     　　* @date 2021/12/29 20:36
     　　*/
    @Override
    public TbArea getAreaByName(String provinceOrCity, String area, String level) {
        List<TbArea> allArea = getAllArea(Constants.AREA_ALL);

        Map<String, TbArea> mapNameArea = new HashMap<>(allArea.size());
        allArea.forEach(tbArea -> {
            String areaName = tbArea.getName();
            Integer level2 = tbArea.getLevel();
            mapNameArea.put(areaName + "_" + level2, tbArea);
        });

        // 三级的区域层级有名称重复的记录，需要从市层级开始取，先取市区，再取子集，根据名称筛选
        if(level.equals("3")){
            TbArea cityArea = mapNameArea.get(provinceOrCity + "_2");
            if(cityArea != null){
                List<TbArea> childAreaList = this.getAreaTree(cityArea.getAreaid(), Constants.AREA_ALL);
                if(CollectionUtils.isNotEmpty(childAreaList)){
                    for (TbArea child : childAreaList) {
                        String name = child.getName();
                        if(name.equals(area)){
                            return child;
                        }
                    }
                }
            }
        }
        TbArea tbArea = mapNameArea.get(provinceOrCity + "_" + level);
        if (null != tbArea){
            return tbArea;
        }
        // todo 目前有点地点用的是简称 有的是全称， 估先冗余一份，根据全称拿不到再根据简称获取
        Map<String, TbArea> mapShortNameArea = new HashMap<>(allArea.size());
        allArea.forEach(t -> {
            String areaName = t.getShortName();
            Integer level2 = t.getLevel();
            mapShortNameArea.put(areaName + "_" + level2, t);
        });

        // 三级的区域层级有名称重复的记录，需要从市层级开始取，先取市区，再取子集，根据名称筛选
        if(level.equals("3")){
            TbArea cityArea = mapShortNameArea.get(provinceOrCity + "_2");
            if(cityArea != null){
                List<TbArea> childAreaList = this.getAreaTree(cityArea.getAreaid(), Constants.AREA_ALL);
                if(CollectionUtils.isNotEmpty(childAreaList)){
                    for (TbArea child : childAreaList) {
                        String name = child.getName();
                        if(name.equals(area)){
                            return child;
                        }
                    }
                }
            }
        }

        return mapShortNameArea.get(provinceOrCity + "_" + level);
    }

    private List<TbArea> listToTree(List<TbArea> list, String pid) {
        List<String> ids = list.stream().map(TbArea::getAreaid).collect(Collectors.toList());
        List<String> topElement = new ArrayList<>(list.size());
        Map<String,List<TbArea>> map = new HashMap<>(16);
        for (int i = list.size() - 1; i >= 0; i--) {
            TbArea entity = list.get(i);
            if(!ids.contains(entity.getPid())){
                topElement.add(entity.getAreaid());
            }
            List<TbArea> parent = Optional.ofNullable(map.get(entity.getPid())).orElseGet(() -> {
                List<TbArea> temp = new ArrayList<>();
                map.put(entity.getPid(),temp);
                return temp;
            });
            parent.add(entity);
            List<TbArea> children = Optional.ofNullable(map.get(entity.getAreaid())).orElseGet(() -> {
                List<TbArea> temp = new ArrayList<>();
                map.put(entity.getAreaid(),temp);
                return temp;
            });
            entity.setChildren(children);
        }
        Stream<TbArea> stream = list.stream();
        if (StringUtils.isNotBlank(pid)) {
            stream = stream.filter(entity -> pid.equals(entity.getPid()));
        } else {
            stream = stream.filter(entity -> topElement.contains(entity.getAreaid()));
        }
        return stream.sorted(Comparator.comparing(TbArea::getOrd,Comparator.nullsLast(Comparator.naturalOrder()))).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void dealArea(UpdateAreaVO updateAreaVO, String user) {
        List<TbArea> allArea = areaMapper.getAreaList(Constants.AREA_ALL);
        if (CollectionUtils.isEmpty(allArea)){
            throw new BusinessException("地区数据为空");
        }
        Map<String, TbArea> areaidMap = allArea.stream().collect(Collectors.toMap(TbArea::getAreaid, Function.identity()));
        Map<String, List<TbArea>> pidGroupMap = allArea.stream().collect(Collectors.groupingBy(TbArea::getPid));

        // 高德的行政区划数据
        List<AMapDistrict> gdList = AMapApiUtil.getAllDistricts();
        Map<String, AMapDistrict> gdCodeMap = gdList.stream().collect(Collectors.toMap(AMapDistrict::getAdcode, Function.identity()));

        // 取最大id,是否向上1000取整
        AtomicInteger maxId = this.getMaxId(allArea, updateAreaVO.isRecodeIdFlag());
        // 取最大areaId,是否向上1000取整
        AtomicInteger maxAreaId = this.getMaxAreaId(allArea, updateAreaVO.isRecodeIdFlag());

        // 处理删除
        this.areaDel(areaidMap, updateAreaVO.getDel(), gdCodeMap);

        // 处理新增
        this.areaAdd(maxId, maxAreaId, areaidMap, pidGroupMap, updateAreaVO.getAdd(), gdCodeMap);

        // 处理改名
        this.areaRename(maxId, maxAreaId, areaidMap, updateAreaVO.getRename(), gdCodeMap);

        // 刷新缓存
        redisUtil.delete(RedisKeyEnum.ALL_AREA, Constants.AREA_ALL);
        redisUtil.delete(RedisKeyEnum.ALL_AREA, Constants.AREA_ONLY_VALID);
        // 记录日志
        areaChangeRecordMapper.insert(new TbAreaChangeRecord(user, updateAreaVO.toString()));
    }

    private void areaDel(Map<String, TbArea> areaidMap, List<UpdateAreaVO.DelData> needHideList, Map<String, AMapDistrict> gdCodeMap) {
        if (CollectionUtils.isEmpty(needHideList)) {
            return;
        }

        boolean idAndNameAllMatch = needHideList.stream().allMatch(x -> areaidMap.get(x.getAreaId()).getName().equals(x.getName()));
        if (!idAndNameAllMatch) {
            throw new BusinessException("area和name不匹配");
        }

        boolean codeInvalid = needHideList.stream().anyMatch(x -> StringUtils.isNotBlank(x.getRelatedAreaCode()) && !gdCodeMap.containsKey(x.getRelatedAreaCode()));
        if (codeInvalid) {
            throw new BusinessException("relatedAreaCode相关行政区划代码不存在");
        }

        List<TbArea> needHideIds = needHideList.stream().map(x -> {
            TbArea area = new TbArea();
            area.setAreaid(x.getAreaId());
            area.setRelatedAreaCode(x.getRelatedAreaCode());
            return area;
        }).collect(Collectors.toList());

        areaMapper.hideByAreaIds(needHideIds);
        log.info("删除数量：{}；明细：{}", needHideList.size(), needHideList);
    }

    private void areaAdd(AtomicInteger id, AtomicInteger areaId, Map<String, TbArea> areaidMap,
                         Map<String, List<TbArea>> pidGroupMap, List<UpdateAreaVO.AddData> needAddList, Map<String, AMapDistrict> gdCodeMap){

        if (CollectionUtils.isEmpty(needAddList)){
            return;
        }

        List<TbArea> addList = new ArrayList<>();

        for (UpdateAreaVO.AddData addData : needAddList) {
            TbArea parentArea = this.getAreaByAreaId(areaidMap, addData.getPAreaId(), addData.getPName(), true);

            AMapDistrict gdNode = gdCodeMap.get(addData.getAreaCode());
            if (gdNode == null) {
                throw new BusinessException("行政区划代码不存在：" + addData.getAreaCode());
            }
            if (!Objects.equals(gdNode.getName(), addData.getName())) {
                throw new BusinessException("行政区划名称与名称不匹配：" + addData.getName() + "，" + gdNode.getName());
            }

            TbArea tbArea = new TbArea();
            tbArea.setId(id.getAndIncrement());
            tbArea.setAreaid(String.valueOf(areaId.getAndIncrement()));
            tbArea.setAreaCode(addData.getAreaCode());
            tbArea.setName(addData.getName());
            tbArea.setShortName(addData.getShortName());
            tbArea.setAbbrSpell(getAbbrSpell(tbArea.getShortName()));
            tbArea.setFullSpell(getFullSpell(tbArea.getShortName()));
            tbArea.setOrd(this.getMaxOrd(pidGroupMap, addData.getPAreaId()));
            tbArea.setSort((short) this.getMaxSort(pidGroupMap, addData.getPAreaId()));
            tbArea.setDisplayable((byte) 1);
            tbArea.setPid(parentArea.getAreaid());
            tbArea.setParentId(parentArea.getId());
            tbArea.setParentAreaCode(parentArea.getAreaCode());
            tbArea.setLevel(parentArea.getLevel() + 1);
            Pair<Double, Double> longitudeAndLatitudeAsDouble = AMapApiUtil.getLongitudeAndLatitudeAsDouble(gdNode);
            tbArea.setLongitude(longitudeAndLatitudeAsDouble.getKey());
            tbArea.setLatitude(longitudeAndLatitudeAsDouble.getValue());

            areaidMap.put(tbArea.getAreaid(), tbArea);
            pidGroupMap.computeIfAbsent(tbArea.getPid(), k -> new ArrayList<>()).add(tbArea);
            addList.add(tbArea);
        }

        areaMapper.batchInsert(addList);
        log.info("新增数量：{}；明细：{}", addList.size(), addList);
    }

    private TbArea getAreaByAreaId(Map<String, TbArea> areaidMap, String areaId, String name, boolean parentFlag) {
        if (!areaidMap.containsKey(areaId)) {
            throw new BusinessException("areaId不存在" + areaId);
        }

        TbArea area = areaidMap.get(areaId);
        if (!area.getName().equals(name) || (parentFlag && area.getLevel() == 3)) {
            throw new BusinessException("areaId错误" + areaId);
        }
        return area;
    }

    private void areaRename(AtomicInteger id, AtomicInteger areaId, Map<String, TbArea> areaidMap, List<UpdateAreaVO.RenameData> needRenameList, Map<String, AMapDistrict> gdCodeMap){

        if (CollectionUtils.isEmpty(needRenameList)){
            return;
        }

        // 删旧的
        List<UpdateAreaVO.DelData> needHideList = needRenameList.stream()
                .map(x -> new UpdateAreaVO.DelData(x.getAreaId(), x.getOldName(), x.getAreaCode())).collect(Collectors.toList());
        areaDel(areaidMap, needHideList, gdCodeMap);

        // 建新的
        List<TbArea> addList = new ArrayList<>();
        for (UpdateAreaVO.RenameData renameData : needRenameList) {
            TbArea oldArea = this.getAreaByAreaId(areaidMap, renameData.getAreaId(), renameData.getOldName(), false);

            AMapDistrict gdNode = gdCodeMap.get(renameData.getAreaCode());
            if (gdNode == null) {
                throw new BusinessException("行政区划代码不存在：" + renameData.getAreaCode());
            }
            if (!Objects.equals(gdNode.getName(), renameData.getName())) {
                throw new BusinessException("行政区划名称与名称不匹配：" + renameData.getName() + "，" + gdNode.getName());
            }

            TbArea tbArea = new TbArea();
            tbArea.setId(id.getAndIncrement());
            tbArea.setAreaid(String.valueOf(areaId.getAndIncrement()));
            tbArea.setAreaCode(renameData.getAreaCode());
            tbArea.setName(renameData.getName());
            tbArea.setShortName(renameData.getShortName());
            tbArea.setAbbrSpell(getAbbrSpell(tbArea.getShortName()));
            tbArea.setFullSpell(getFullSpell(tbArea.getShortName()));
            tbArea.setDisplayable((byte) 1);
            Pair<Double, Double> longitudeAndLatitudeAsDouble = AMapApiUtil.getLongitudeAndLatitudeAsDouble(gdNode);
            tbArea.setLongitude(longitudeAndLatitudeAsDouble.getKey());
            tbArea.setLatitude(longitudeAndLatitudeAsDouble.getValue());

            tbArea.setPid(oldArea.getPid());
            tbArea.setParentId(oldArea.getParentId());
            tbArea.setParentAreaCode(oldArea.getParentAreaCode());
            tbArea.setOrd(oldArea.getOrd());
            tbArea.setSort(oldArea.getSort());
            tbArea.setLevel(oldArea.getLevel());
            addList.add(tbArea);
        }

        areaMapper.batchInsert(addList);
        log.info("改名新增数量：{}；明细：{}", addList.size(), addList);
    }

    private AtomicInteger getMaxId(List<TbArea> allArea, boolean recodeIdFlag) {
        OptionalInt maxIdOptional = allArea.stream().mapToInt(TbArea::getId).max();
        int maxId = maxIdOptional.orElseThrow(() -> new BusinessException("取最大id错误"));
        return new AtomicInteger(recodeIdFlag ? ((maxId + 999) / 1000) * 1000 : maxId + 1);
    }

    private AtomicInteger getMaxAreaId(List<TbArea> allArea, boolean recodeIdFlag) {
        OptionalInt maxAreaIdOptional = allArea.stream().mapToInt(x -> Integer.parseInt(x.getAreaid())).max();
        int maxAreaId = maxAreaIdOptional.orElseThrow(() -> new BusinessException("取最大areaId错误"));
        return new AtomicInteger(recodeIdFlag ? ((maxAreaId + 999) / 1000) * 1000 : maxAreaId + 1);
    }

    private int getMaxOrd(Map<String, List<TbArea>> pidGroupMap, String pid) {
        List<TbArea> areas = pidGroupMap.getOrDefault(pid, Collections.emptyList());
        if (!areas.isEmpty()) {
            return areas.stream().mapToInt(TbArea::getOrd).max().orElse(0) + 1;
        }
        return 1;
    }

    private int getMaxSort(Map<String, List<TbArea>> pidGroupMap, String pid) {
        List<TbArea> areas = pidGroupMap.getOrDefault(pid, Collections.emptyList());
        if (!areas.isEmpty()) {
            return areas.stream().mapToInt(TbArea::getSort).max().orElse(0) + 1;
        }
        return 1;
    }

    public static String getAbbrSpell(String name){
        return PinyinUtil.getFirstLettersUp(name);
    }

    private static final Pattern PATTERN = Pattern.compile("([a-z])([a-z]*)", Pattern.CASE_INSENSITIVE);

    /**
     * 全拼，并将第一个字母转为大写，其余字母转为小写
     * @param name
     * @return
     */
    public static String getFullSpell(String name) {
        if (StringUtils.isEmpty(name)) {
            return null;
        }

        String pinyinAll = PinyinUtil.getPinyinAll(name); // 获取名称的拼音形式
        Matcher matcher = PATTERN.matcher(pinyinAll); // 创建匹配器对象

        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String group1 = matcher.group(1); // 获取第一个分组的匹配结果
            String group2 = matcher.group(2); // 获取第二个分组的匹配结果

            // 构建替换字符串：将第一个字母转为大写，其余字母转为小写
            String replacement = group1.toUpperCase() + group2.toLowerCase();

            matcher.appendReplacement(result, replacement);
        }

        return matcher.appendTail(result).toString();
    }
    @Transactional(rollbackFor = Exception.class)
    public void fillAreaCode(){
        List<TbArea> allArea = areaMapper.getAreaList(Constants.AREA_ALL);
        if (CollectionUtils.isEmpty(allArea)){
            throw new BusinessException("地区数据为空");
        }
        check(allArea);

        Map<String, TbArea> areaidMap = allArea.stream().collect(Collectors.toMap(TbArea::getAreaid, Function.identity()));
        Map<String, AtomicInteger> pCodeAndCount = new HashMap<>();
       List<TbArea> areaList = new ArrayList<>();
        // 先处理2级
        areaList.addAll(fillAreaCode(allArea, areaidMap, pCodeAndCount, 2));
        // 处理3级
        areaList.addAll(fillAreaCode(allArea, areaidMap, pCodeAndCount, 3));

        if (CollectionUtils.isNotEmpty(areaList)){
            List<TbArea> distinctList = areaList.stream().distinct().collect(Collectors.toList());
            log.info("{}-{}", areaList.size(), distinctList.size());
            areaMapper.batchUpdateAreaCode(distinctList);
        }
    }

    private List<TbArea> fillAreaCode(List<TbArea> allArea, Map<String, TbArea> areaidMap, Map<String, AtomicInteger> pCodeAndCount, Integer level) {
        List<TbArea> areaList = new ArrayList<>();
        List<TbArea> cityPCodeNull = allArea.stream().filter(x -> Objects.equals(x.getLevel(), level) && StringUtils.isEmpty(x.getParentAreaCode())).collect(Collectors.toList());
        cityPCodeNull.forEach(item -> {
            if (areaidMap.containsKey(item.getPid())) {
                TbArea pArea = areaidMap.get(item.getPid());
                item.setParentAreaCode(pArea.getAreaCode());
                areaList.add(item);
            }
        });

        List<TbArea> cityCodeNull = allArea.stream().filter(x -> Objects.equals(x.getLevel(), level) && StringUtils.isEmpty(x.getAreaCode())).collect(Collectors.toList());
        cityCodeNull.forEach(item -> {
            String parentAreaCode = item.getParentAreaCode();
            int number = pCodeAndCount.computeIfAbsent(parentAreaCode, k -> new AtomicInteger(99)).getAndDecrement();
            String areaCode;
            if (level == 2){
                areaCode = parentAreaCode.substring(0, 2) + String.format("%02d", number) + "00";
            }else {
                areaCode = parentAreaCode.substring(0, 4) + String.format("%02d", number);
            }

            item.setAreaCode(areaCode);
            areaList.add(item);
        });

        return areaList;
    }

    private void check(List<TbArea> allArea) {
        Map<String, TbArea> areaidMap = allArea.stream().collect(Collectors.toMap(TbArea::getAreaid, Function.identity()));
        Map<Integer, TbArea> idMap = allArea.stream().collect(Collectors.toMap(TbArea::getId, Function.identity()));
        Map<String, TbArea> areaCodeMap = allArea.stream()
                .filter(x -> StringUtils.isNotEmpty(x.getAreaCode())).collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));

        boolean flag = allArea.stream().filter(x -> x.getLevel() != 0).allMatch(x -> areaidMap.containsKey(x.getPid()));
        if (!flag){
            throw new BusinessException("areaId父子关系错误");
        }

        List<TbArea> idErrList = new ArrayList<>();
        for (TbArea tbArea : allArea) {
            if (idMap.containsKey(tbArea.getParentId()) && Objects.equals(tbArea.getPid(), idMap.get(tbArea.getParentId()).getAreaid())){
                continue;
            }
            idErrList.add(tbArea);
        }

        List<TbArea> codeErrList = new ArrayList<>();
        for (TbArea tbArea : allArea) {
            if (StringUtils.isEmpty(tbArea.getParentAreaCode())){
                continue;
            }
            if (areaCodeMap.containsKey(tbArea.getParentAreaCode()) &&
                    Objects.equals(tbArea.getPid(), areaCodeMap.get(tbArea.getParentAreaCode()).getAreaid())){
                continue;
            }
            codeErrList.add(tbArea);
        }

        log.info("idErrList:{};codeErrList:{}", idErrList, codeErrList);

        List<TbArea> collect1 = allArea.stream().filter(x -> StringUtils.isNotEmpty(x.getParentAreaCode()) && !x.getParentAreaCode().substring(4).equals("00")).collect(Collectors.toList());

        List<TbArea> collect2 = allArea.stream().filter(x -> (x.getLevel() == 1 || x.getLevel() == 2) && StringUtils.isNotEmpty(x.getAreaCode()) && !x.getAreaCode().substring(4).equals("00")).collect(Collectors.toList());


        log.info("collect1:{};collect2:{}", collect1, collect2);

    }

    @Override
    public AreaDiffResult compareHandler() {
        AreaDiffResult areaDiffResult = new AreaDiffResult();

        // 库里的数据
        List<TbArea> dbAreaList = areaMapper.getAreaList(Constants.AREA_ONLY_VALID);
        AreaUtil.setNamePath(dbAreaList);
        List<TbArea> dbList = dbAreaList.stream()
                .filter(x -> !AreaUtil.SKIP_NODE.containsKey(x.getNamePath()) && AreaUtil.isValidRegionCode(x.getAreaCode()))
                .collect(Collectors.toList());
        // 高德的数据
        List<AMapDistrict> gdList = AMapApiUtil.getAllDistricts();

        Set<String> allCode = Stream.concat(
                dbList.stream().map(TbArea::getAreaCode),
                gdList.stream().map(AMapDistrict::getAdcode)
        ).collect(Collectors.toSet());

        Map<String, TbArea> dbMap = dbList.stream().collect(Collectors.toMap(TbArea::getAreaCode, Function.identity()));
        Map<String, AMapDistrict> gdMap = gdList.stream().collect(Collectors.toMap(AMapDistrict::getAdcode, Function.identity()));

        for (String code : allCode) {
            TbArea dbNode = dbMap.get(code);
            AMapDistrict gdNode = gdMap.get(code);

            AreaDiff areaDiff = new AreaDiff();
            areaDiff.setAreaCode(code);
            areaDiff.setNewAreaCode(code);

            if (dbNode == null) {
                areaDiff.setNewNamePath(gdNode.getNamePath());
                areaDiffResult.getAddList().add(areaDiff);
            } else if (gdNode == null) {
                areaDiff.setNamePath(dbNode.getNamePath());
                areaDiffResult.getDelList().add(areaDiff);
            } else {
                String dbName = AreaUtil.NAME_COVER_MAP.getOrDefault(dbNode.getAreaCode(), dbNode.getName());
                String aMapName = gdNode.getName();

                if (!dbName.equals(aMapName)) {
                    areaDiff.setNamePath(dbName);
                    areaDiff.setNewNamePath(aMapName);
                    areaDiffResult.getNameChangeList().add(areaDiff);
                } else {
                    areaDiff.setNamePath(dbNode.getNamePath());
                    areaDiff.setNewNamePath(gdNode.getNamePath());
                    Pair<String, String> longitudeAndLatitude = AMapApiUtil.getLongitudeAndLatitudeAsString(gdNode);

                    if (!Objects.equals(String.valueOf(dbNode.getLongitude()), longitudeAndLatitude.getKey()) || !Objects.equals(String.valueOf(dbNode.getLatitude()), longitudeAndLatitude.getValue())) {
                        areaDiff.setRemark("db：[" + dbNode.getLongitude() + "," + dbNode.getLatitude() + "]\tgd：[" + longitudeAndLatitude.getKey() + "," + longitudeAndLatitude.getValue() + "]");
                        areaDiffResult.getDataChangeList().add(areaDiff);
                    }
                }
            }
        }

        return areaDiffResult;
    }
}
