package com.glodon.qydata.service.standard.feature.impl;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.glodon.qydata.common.RequestContent;
import com.glodon.qydata.common.annotation.BusinessCache;
import com.glodon.qydata.common.constant.BusinessConstants;
import com.glodon.qydata.common.constant.Constants;
import com.glodon.qydata.common.constant.OperateConstants;
import com.glodon.qydata.common.enums.ExpressionTypeEnum;
import com.glodon.qydata.common.enums.RedisKeyEnum;
import com.glodon.qydata.common.enums.ResponseCode;
import com.glodon.qydata.dto.BatchAddExpressionDto;
import com.glodon.qydata.dto.ExpressionItem;
import com.glodon.qydata.entity.standard.category.CommonProjCategory;
import com.glodon.qydata.entity.standard.expression.ZbStandardsExpression;
import com.glodon.qydata.entity.standard.feature.ProjectFeature;
import com.glodon.qydata.entity.standard.feature.ProjectFeatureCategoryView;
import com.glodon.qydata.entity.standard.feature.TzExcel;
import com.glodon.qydata.entity.standard.trade.ZbStandardsTrade;
import com.glodon.qydata.exception.BusinessException;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionMapper;
import com.glodon.qydata.mapper.standard.expression.ZbStandardsExpressionSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureCategoryViewSelfMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureMapper;
import com.glodon.qydata.mapper.standard.feature.ProjectFeatureSelfMapper;
import com.glodon.qydata.mapper.standard.trade.ZbStandardsTradeMapper;
import com.glodon.qydata.service.init.enterprise.InitTradeService;
import com.glodon.qydata.service.standard.category.CommonProjCategoryService;
import com.glodon.qydata.service.standard.category.impl.CommonProjectCategoryUsedService;
import com.glodon.qydata.service.standard.expression.IExpressionService;
import com.glodon.qydata.service.standard.feature.FeatureCommonHandler;
import com.glodon.qydata.service.standard.feature.IProjectFeatureService;
import com.glodon.qydata.service.standard.historyDS.CustomerCodeConvertService;
import com.glodon.qydata.service.standard.trade.IStandardsTradeService;
import com.glodon.qydata.service.standard.unit.IZbUnitService;
import com.glodon.qydata.service.system.IGlodonUserService;
import com.glodon.qydata.service.system.PublishInfoService;
import com.glodon.qydata.util.CategoryUtil;
import com.glodon.qydata.util.ExcelUtil;
import com.glodon.qydata.util.RedisUtil;
import com.glodon.qydata.util.snowflake.SnowflakeIdUtils;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureCategoryViewVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureDsVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureFilterVO;
import com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO;
import com.glodon.qydata.vo.standard.trade.StandardTradeVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_FALSE;
import static com.glodon.qydata.common.constant.Constants.ZbFeatureConstants.WHETHER_TRUE;

/**
 * @program: ideaworkspace
 * @description: 工程特征业务逻辑处理层
 * @author: zhaohy-c
 * @create: 2021-01-12 16:37
 */
@Service("featureStandardService")
@Slf4j
public class ProjectFeatureServiceImpl implements IProjectFeatureService {
    @Autowired
    private ProjectFeatureMapper projectFeatureMapper;

    @Autowired
    private ProjectFeatureCategoryViewMapper projectFeatureCategoryViewMapper;

    @Autowired
    private ProjectFeatureSelfMapper projectFeatureSelfMapper;

    @Autowired
    private ProjectFeatureCategoryViewSelfMapper projectFeatureCategoryViewSelfMapper;

    @Autowired
    private IExpressionService expressionService;
    @Autowired
    private ZbStandardsTradeMapper tradeMapper;

    @Autowired
    private ZbStandardsExpressionMapper zbStandardsExpressionMapper;

    @Autowired
    private CommonProjCategoryService commonProjCategoryService;

    @Autowired
    private IGlodonUserService glodonUserService;

    @Autowired
    private IStandardsTradeService tradeService;

    @Autowired
    private CustomerCodeConvertService customerCodeConvertService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ZbStandardsExpressionSelfMapper zbStandardsExpressionSelfMapper;

    @Autowired
    private CommonProjectCategoryUsedService commonProjectCategoryUsedService;

    @Autowired
    private InitTradeService initTradeService;

    @Autowired
    private FeatureCommonHandler featureCommonHandler;

    @Autowired
    private IZbUnitService zbUnitService;

    @Autowired
    PublishInfoService publishInfoServiceImpl;

    /**
     * @description: 填充 1.名称+类型+枚举值 2.创建人、更新人姓名
     * @param featureId
     * @return com.gcj.zblib.standardData.feature.vo.ProjectFeatureResultVO
     * <AUTHOR>
     * @date 2021/11/3 18:09
     */
    @Override
    public ProjectFeatureResultVO getCompleteFeatureByPrimaryKey(Long featureId, Integer viewType, String categoryCode, List<CommonProjCategory> categoryListTree){
        ProjectFeatureResultVO resultVO = new ProjectFeatureResultVO();
        ProjectFeature newProjectFeature = getFeatureByPrimaryKey(featureId, categoryListTree);
        featureCommonHandler.fillExpressionCode(newProjectFeature);
        BeanUtils.copyProperties(newProjectFeature, resultVO);

        // 创建人、更新人姓名
        Long createGlobalId = resultVO.getCreateGlobalId();
        if (Objects.nonNull(createGlobalId)){
            String creatUserName = glodonUserService.getUserName(createGlobalId.toString(), null);
            resultVO.setCreateGlobalName(creatUserName);
        }
        Long updateGlobalId = resultVO.getUpdateGlobalId();
        if (Objects.nonNull(updateGlobalId)){
            String updateUserName = glodonUserService.getUserName(updateGlobalId.toString(), null);
            resultVO.setUpdateGlobalName(updateUserName);
        }
        // 分类视图的projectType处理
        if (Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW.equals(viewType)) {
            resultVO.setProjectType(featureCommonHandler.getDealProjectType(resultVO.getProjectType(), categoryCode, Constants.ZbFeatureConstants.JSON_DEAL_1));
        }
        return resultVO;
    }

    /**
     * @description: 解析projectType获取一级工程分类
     * @param projectType
     * @return java.util.Set<java.lang.String>
     * <AUTHOR>
     * @date 2021/10/25 11:06
     */
    private Set<String> getCategoryCode(String projectType){
        HashSet<String> categoryCodeSet = new HashSet<>();
        if (StringUtils.isNotEmpty(projectType)){
            Map<String, Object> parse = JSONObject.parseObject(projectType, Map.class);
            if (Objects.nonNull(parse)){
                Object projectTypeJson = parse.get(BusinessConstants.PROJECT_TYPE);
                if(Objects.nonNull(projectTypeJson)){
                    JSONArray jsonArray = JSONArray.parseArray(projectTypeJson.toString());
                    for (Object o : jsonArray) {
                        String o1 = o.toString();
                        JSONArray objects = JSONArray.parseArray(o1);
                        String categoryCode = (String) objects.get(0);
                        categoryCodeSet.add(categoryCode);
                    }
                }
            }
        }
        return categoryCodeSet;
    }

    /**
     * @description: 根据项目特征id查询项目特征信息
     * @param id
     * @return com.gcj.zblib.standardData.feature.entity.ProjectFeature
     * <AUTHOR>
     * @date 2021/10/21 21:05
     */
    public ProjectFeature getFeatureByPrimaryKey(Long id, List<CommonProjCategory> categoryListTree) {
        if (id == null) {
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "项目特征ID不能为空");
        }
        ProjectFeature projectFeature = projectFeatureMapper.selectByPrimaryKey(id);
        if (null == projectFeature) {
            //此工程特征已被删除，或不存在，请检查
            throw new BusinessException(ResponseCode.ERROR, "此工程特征已被删除，或不存在，请检查");
        }

        // //转化projectType mark_projectType
        projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD));
        return projectFeature;
    }

    /**
     　　* @description: 根据专业ID逻辑删除专业下携带的工程特征(企业+个人)
     　　* @param  tradeId 专业id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/25 10:36
     　　*/
    @Override
    public void deleteByTradeId(Long tradeId){
        projectFeatureMapper.deleteByTradeId(tradeId);
        projectFeatureCategoryViewMapper.deleteByTradeId(tradeId);
        projectFeatureSelfMapper.deleteByTradeId(tradeId);
        projectFeatureCategoryViewSelfMapper.deleteByTradeId(tradeId);
    }

    /**
     * @description: 查询工程特征列表(专业视图)
     * @param filterVO
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/10/25 20:13
     */
    @Override
    //@BusinessCache(customerCode = "${customerCode}")
    public List<ProjectFeatureResultVO> featureTradeView(String customerCode, ProjectFeatureFilterVO filterVO) {
        Long tradeId = filterVO.getTradeId();
        if (tradeId == null){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "专业TradeId不能为空...");
        }

        Integer type = filterVO.getType();
        if (type == null){
            type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        }

        // 专业视图
        List<ProjectFeature> projectFeatureList = projectFeatureMapper.selectByTradeIdAndType(tradeId, type);

        // 如果没查到数据，检查是否初始化
        if (CollectionUtils.isEmpty(projectFeatureList)){
            initTradeService.initData(customerCode);
            projectFeatureList = projectFeatureMapper.selectByTradeIdAndType(tradeId, type);
        }

        //转化projectType mark_projectType
        if (CollUtil.isNotEmpty(projectFeatureList)) {
            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
            projectFeatureList.forEach(projectFeature -> projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD)));
        }

        featureCommonHandler.fillExpressionCode(projectFeatureList, customerCode, type);

        return featureCommonHandler.convertToResultVO(projectFeatureList, filterVO.getViewType(), filterVO.getCategoryCode(), filterVO.getIsSkipUserName());
    }

    /**
     * @description: 给企业创建系统内置的副本
     * @param customerCode
     * @return void
     * <AUTHOR>
     * @date 2021/11/11 15:09
     */
    @Override
    public void initFeatureData(String customerCode, Integer type){
        if (projectFeatureMapper.selectInit(customerCode, type) != 0) {
            return;
        }

        if (type == null) {
            type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        }
        Map<Long, Long> sysAndNewTradeIdMap = getTradeIdMap(customerCode);

        List<ProjectFeature> list = projectFeatureMapper.selectAll(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, type);

        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();
        // 获取该企业下特征的分类视图，并按分类分组，做排序用
        List<ProjectFeatureCategoryView> customerCategoryViewList = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(null, customerCode,type);
        Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);
        if (CollectionUtils.isNotEmpty(customerCategoryViewList)){
            groupMap = customerCategoryViewList.stream().collect(Collectors.groupingBy(ProjectFeatureCategoryView::getCategoryCode));
        }

        for (ProjectFeature projectFeature : list) {
            projectFeature.setId(SnowflakeIdUtils.getNextId());
            projectFeature.setCustomerCode(customerCode);
            projectFeature.setCreateTime(null);
            projectFeature.setUpdateTime(null);
            Long sysTradeId = projectFeature.getTradeId();
            if (sysAndNewTradeIdMap.containsKey(sysTradeId)){
                projectFeature.setTradeId(sysAndNewTradeIdMap.get(sysTradeId));
            }else {
                log.error("企业：{}-未匹配到默认工程专业：{}", customerCode, sysTradeId);
            }
            List<ProjectFeatureCategoryView> projectFeatureCategoryViews = initCategoryView(projectFeature, groupMap);
            projectFeatureCategoryViewList.addAll(projectFeatureCategoryViews);
        }
        projectFeatureMapper.batchInsert(list);
        // 插入分类视图
        if (CollectionUtils.isNotEmpty(projectFeatureCategoryViewList)) {
            projectFeatureCategoryViewMapper.saveBatch(projectFeatureCategoryViewList);
        }

        // 初始化计算口径
        initExpression(customerCode, type);
    }

    /**
     * @description: 获取工程特征内置数据
     * @param
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeature>
     * <AUTHOR>
     * @date 2021/11/19 9:47
     */
    @Override
    public List<ProjectFeatureDsVO> getSystemFeature() {
        // 获取系统内置特征
        List<ProjectFeature> projectFeatures = projectFeatureMapper.selectAll(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, Constants.ZbFeatureConstants.CATEGORY_DEFAULT_TYPE);
        // 转换 名称+类型+枚举值
        List<ProjectFeatureResultVO> resultVoList = featureCommonHandler.convertToResultVO(projectFeatures, null, null, null);
        // 获取系统内工程专业id和名称的对应关系
        List<ZbStandardsTrade> systemStandardsTrades = tradeMapper.selectListByCustomerCodeAndType(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);
        Map<Long, String> tradeIdAndNameMap = systemStandardsTrades.stream().collect(Collectors.toMap(ZbStandardsTrade::getId, ZbStandardsTrade::getDescription, (v1, v2) -> v2));
        // 将系统的工程专业id转换成名称
        List<ProjectFeatureDsVO> dsVoList = new ArrayList<>();
        for (ProjectFeatureResultVO resultVO : resultVoList) {
            ProjectFeatureDsVO projectFeatureDsVO = new ProjectFeatureDsVO();
            BeanUtils.copyProperties(resultVO, projectFeatureDsVO);
            if (!tradeIdAndNameMap.containsKey(projectFeatureDsVO.getTradeId())){
                log.error("内置数据出错了,内置特征名：{},对应专业id:{}", projectFeatureDsVO.getName(), projectFeatureDsVO.getTradeId());
                throw new BusinessException(ResponseCode.FAILURE, "内置数据出错了...");
            }
            projectFeatureDsVO.setTradeName(tradeIdAndNameMap.get(projectFeatureDsVO.getTradeId()));
            projectFeatureDsVO.setTradeId(null);
            dsVoList.add(projectFeatureDsVO);
        }
        return dsVoList;
    }

    /**
     * @description: 工程特征数据同步
     * @param featureDsVOList
     * @return void
     * <AUTHOR>
     * @date 2021/11/19 11:50
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void featureHistoryDs(List<ProjectFeatureDsVO> featureDsVOList, String oldCustomerCode) {
        log.info("开始同步工程特征...");
        long startTime = System.currentTimeMillis();

        if (StringUtils.isEmpty(oldCustomerCode)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "企业编码不能为空...");
        }

        // 获取新企业编码
        String customerCode = customerCodeConvertService.getCustomerCode(oldCustomerCode);
        long time1 = System.currentTimeMillis();
        log.info("企业编码转换耗时：{}", time1-startTime);
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        // 为了支持多次同步同一企业，首先删除此批企业下所有特征相关数据
        zbStandardsExpressionMapper.deleteByCustomerCode(customerCode, type);
        projectFeatureMapper.deleteByCustomerCode(customerCode, type);
        projectFeatureCategoryViewMapper.deleteByCustomerCode(customerCode, type);

        zbStandardsExpressionSelfMapper.deleteSelfByCustomerCode(customerCode, type);
        projectFeatureSelfMapper.deleteBySelfCustomerCode(customerCode, type);
        projectFeatureCategoryViewSelfMapper.deleteBySelfCustomerCode(customerCode, type);
        long time2 = System.currentTimeMillis();
        log.info("删除企业工程特征相关数据耗时：{}", time2-time1);

        List<ProjectFeature> featureList = new ArrayList<>();
        List<ProjectFeatureCategoryView> featureCategoryViewList = new ArrayList<>();
        // 单位
        Map<Long, String> expressionIdUnitMap = new HashMap<>(16);
        Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);

        // 获取工程分类全选json串
        String projectType = getProjectType();

        // 获取内置特征项数据
        List<ZbStandardsExpression> zbStandardsExpressions = getSystemExpression();

        // 复制给当前企业
        List<Long> expressionIdList = SnowflakeIdUtils.getNextId(zbStandardsExpressions.size());
        for (int i = 0; i < zbStandardsExpressions.size(); i++) {
            Long nextId = expressionIdList.get(i);
            zbStandardsExpressions.get(i).setId(nextId);
            zbStandardsExpressions.get(i).setQyCode(customerCode);
            zbStandardsExpressions.get(i).setCreateTime(null);
            zbStandardsExpressions.get(i).setType(type);
        }

        for (ProjectFeatureDsVO projectFeatureDsVO : featureDsVOList) {
            ProjectFeature projectFeature = new ProjectFeature();
            BeanUtils.copyProperties(projectFeatureDsVO, projectFeature);
            projectFeature.setId(SnowflakeIdUtils.getNextId());
            projectFeature.setCustomerCode(customerCode);
            projectFeature.setType(type);
            projectFeature.setIsSync(Constants.ZbFeatureConstants.WHETHER_FALSE);
            projectFeature.setIsDefault(Constants.ZbFeatureConstants.WHETHER_FALSE);
            projectFeature.setIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
            projectFeature.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);

            // 处理expressionId
            Long expressionId = getExpressionId(projectFeatureDsVO.getName(), projectFeatureDsVO.getTypeCode(),
                    projectFeatureDsVO.getOption(), customerCode, projectFeatureDsVO.getCreateGlobalId(), zbStandardsExpressions);
            projectFeature.setExpressionId(expressionId);
            String unit = projectFeatureDsVO.getUnit();
            if (StringUtils.isNotEmpty(unit)){
                expressionIdUnitMap.put(expressionId, unit);
            }

            // 工程分类（默认全选）
            if (StringUtils.isEmpty(projectFeature.getProjectType())){
                projectFeature.setProjectType(projectType);
            }
            // 是否启用（默认全部启用）
            projectFeature.setIsUsing(WHETHER_TRUE);
            // 是否计算口径（数值类默认是计算口径）
            if (Constants.ZbExpressionConstants.TYPE_NUMBER.equals(projectFeatureDsVO.getTypeCode())){
                projectFeature.setIsExpression(WHETHER_TRUE);
            }
            featureList.add(projectFeature);
            // 增加分类视图
            List<ProjectFeatureCategoryView> projectFeatureCategoryViews = initCategoryView(projectFeature, groupMap);
            featureCategoryViewList.addAll(projectFeatureCategoryViews);
        }

        // 处理计算口径
        dealExpressionDict(zbStandardsExpressions, expressionIdUnitMap);
        long time5 = System.currentTimeMillis();
        log.info("数据处理总耗时：{}", time5-startTime);

        log.info("开始入库...");
        if (CollectionUtils.isNotEmpty(zbStandardsExpressions)) {
            zbStandardsExpressionMapper.batchInsert(zbStandardsExpressions);
            log.info("zbStandardsExpressionMapper入库数：{}", zbStandardsExpressions.size());
        }
        long time6 = System.currentTimeMillis();
        log.info("批量插入特征项耗时：{}", time6-time5);

        if (CollectionUtils.isNotEmpty(featureList)) {
            projectFeatureMapper.batchInsert(featureList);
            log.info("projectFeatureMapper入库数：{}", featureList.size());
        }
        long time7 = System.currentTimeMillis();
        log.info("批量插入特征耗时：{}", time7-time6);

        if (CollectionUtils.isNotEmpty(featureCategoryViewList)) {
            projectFeatureCategoryViewMapper.saveBatch(featureCategoryViewList);
            log.info("projectFeatureCategoryViewMapper入库数：{}", featureCategoryViewList.size());
        }
        long time8 = System.currentTimeMillis();
        log.info("耗时批量插入分类视图：{}", time8-time7);

        long endTime = System.currentTimeMillis();
        log.info("入库耗时：{}", endTime-time5);
        log.info("总耗时：{}", endTime-startTime);
    }

    /**
     * @description: 工程分类全选
     * @return
     * <AUTHOR>
     * @date 2022/3/7 18:15
     */
    public String getProjectType(){
        String projectType = redisUtil.getString(RedisKeyEnum.DS_PROJECT_TYPE);
        if (StringUtils.isNotEmpty(projectType)){
            return projectType;
        }

        Map<String, List<List<String>>> systemCategoryMap = commonProjCategoryService.getSystemCategory();
        List<List<String>> projectTypeList = new ArrayList();
        for (List<List<String>> value : systemCategoryMap.values()) {
            projectTypeList.addAll(value);
        }
        Map<String, List<List<String>>> map = new HashMap<>(16);
        map.put(BusinessConstants.PROJECT_TYPE, projectTypeList);
        projectType = JSONObject.toJSONString(map);
        redisUtil.setString(RedisKeyEnum.DS_PROJECT_TYPE, projectType);
        return projectType;
    }

    /**
     * @description: 获取内置特征项数据
     * @return
     * <AUTHOR>
     * @date 2022/3/7 20:47
     */
    public List<ZbStandardsExpression> getSystemExpression(){
        List<ZbStandardsExpression> expressionsList = redisUtil.getListObject(RedisKeyEnum.DS_SYSTEM_EXPRESSION, ZbStandardsExpression.class);
        if (CollectionUtils.isNotEmpty(expressionsList)){
            return expressionsList;
        }
        expressionsList = expressionService.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, Constants.ZbFeatureConstants.CATEGORY_DEFAULT_TYPE);
        redisUtil.setList(RedisKeyEnum.DS_SYSTEM_EXPRESSION, expressionsList);
        return expressionsList;
    }


    /**
     * @description: 特征项处理
     * @param name
     * @param typeCode
     * @param option
     * @param customCode
     * @param globalId
     * @return java.lang.Long
     * <AUTHOR>
     * @date 2021/11/19 14:48
     */
    public Long getExpressionId(String name, String typeCode, String option, String customCode, Long globalId,
                        List<ZbStandardsExpression> allExpressions) {
        // 名称+类型
        Map<String, ZbStandardsExpression> nameTypeOptionMap = allExpressions.stream().
                collect(Collectors.toMap(x -> x.getName() + x.getTypeCode() + x.getOption(), Function.identity(), (v1, v2) -> v2));
        // 名称+类型+枚举值
        Map<String, ZbStandardsExpression> nameTypeMap = allExpressions.stream().
                collect(Collectors.toMap(x -> x.getName() + x.getTypeCode(), Function.identity(), (v1, v2) -> v2));

        // 查询是否存在（名称+类型+枚举值）
        ZbStandardsExpression exist;
        // 单项和多选需要考虑枚举值是否相等
        if (Constants.ZbExpressionConstants.TYPE_SELECT.equals(typeCode) || Constants.ZbExpressionConstants.TYPE_SELECTS.equals(typeCode)) {
            exist = nameTypeOptionMap.get(name + typeCode + option);
        }else {
            exist = nameTypeMap.get(name + typeCode);
        }

        if (Objects.nonNull(exist)) {
            return exist.getId();
        }

        // 不存在就新增
        ZbStandardsExpression zbStandardsExpression = new ZbStandardsExpression();
        zbStandardsExpression.setId(SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setExpressionCode("K_"+SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setQyCode(customCode);
        zbStandardsExpression.setName(name);
        zbStandardsExpression.setTypeCode(typeCode);
        zbStandardsExpression.setOption(option);
        zbStandardsExpression.setCreateGlobalId(globalId);
        zbStandardsExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsUsable(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_FALSE);

        allExpressions.add(zbStandardsExpression);
        return zbStandardsExpression.getId();
    }

    /**
     * @description: 批量加入口径字典
     * @param zbStandardsExpressions
     * @param expressionIdUnitMap
     * @return void
     * <AUTHOR>
     * @date 2021/11/19 15:11
     */
    public void dealExpressionDict(List<ZbStandardsExpression> zbStandardsExpressions, Map<Long, String> expressionIdUnitMap) {
        // 获取最大口径排序
        ZbStandardsExpression maxExpression = zbStandardsExpressions.stream().filter(x -> Objects.nonNull(x.getExpressionOrd()))
                .max((v1, v2) -> v1.getExpressionOrd() > v2.getExpressionOrd() ? 1 : -1).orElse(new ZbStandardsExpression());
        int maxExpressionOrd = Optional.ofNullable(maxExpression.getExpressionOrd()).orElse(0);
        int num = 1;

        for (ZbStandardsExpression expression : zbStandardsExpressions) {
            if (Constants.ZbExpressionConstants.TYPE_NUMBER.equals(expression.getTypeCode())){
                Integer isExpression = expression.getIsExpression();
                // 1、相同走历史，应该保持不变  2、删除又新增，走新增项的创建人，单位清空，启用状态回到默认
                if (isExpression != null && Constants.ZbExpressionConstants.WHETHER_TRUE == isExpression){
                    continue;
                }
                expression.setIsExpression(Constants.ZbExpressionConstants.WHETHER_TRUE);
                expression.setExpressionCreateGlobalId(expression.getCreateGlobalId());
                expression.setExpressionCreateTime(expression.getCreateTime());
                expression.setExpressionOrd(maxExpressionOrd + num++);

                Long expressionId = expression.getId();
                if (expressionIdUnitMap.containsKey(expressionId)){
                    //计算口径单位不为空则启用
                    expression.setExpressionIsUsing(Constants.ZbExpressionConstants.WHETHER_TRUE);
                    expression.setUnit(expressionIdUnitMap.get(expressionId));
                }else {
                    //计算口径单位为空则不启用
                    expression.setExpressionIsUsing(Constants.ZbExpressionConstants.WHETHER_FALSE);
                }
            }
        }
    }

    /**
     * @description: 给企业创建系统内置的副本-专业id转换
     * @param customerCode
     * @return java.util.Map<java.lang.Long, java.lang.Long>
     * <AUTHOR>
     * @date 2021/11/11 15:49
     */
    public Map<Long, Long> getTradeIdMap(String customerCode){
        Map<Long, Long> sysAndNewTradeIdMap = new HashMap<>(16);
        List<ZbStandardsTrade> systemStandardsTrades = tradeMapper.selectListByCustomerCodeAndType(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);
        List<ZbStandardsTrade> zbStandardsTrades = tradeMapper.selectListByCustomerCodeAndType(customerCode, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);
        Map<String, Long> descriptionAndIdMap = zbStandardsTrades.stream().collect(Collectors.toMap(ZbStandardsTrade::getDescription, ZbStandardsTrade::getId, (v1, v2) -> v1));

        for (ZbStandardsTrade zbStandardsTrade : systemStandardsTrades) {
            String description = zbStandardsTrade.getDescription();
            if (descriptionAndIdMap.containsKey(description)) {
                sysAndNewTradeIdMap.put(zbStandardsTrade.getId(), descriptionAndIdMap.get(description));
            }else {
                log.error("企业：{}-未找到默认工程专业：{}", customerCode, zbStandardsTrade.getDescription());
            }
        }
        return sysAndNewTradeIdMap;
    }

    /**
     * @description: 给企业创建系统内置的副本-特征项id装换
     * @param customerCode
     * @return java.util.Map<java.lang.Long, java.lang.Long>
     * <AUTHOR>
     * @date 2021/11/11 15:49
     */
    public Map<Long, Long> initExpression(String customerCode, Integer type){
        Map<Long, Long> sysAndNewExpressionIdMap = new HashMap<>();
        // 先把计算口径表复制一份
        List<ZbStandardsExpression> zbStandardsExpressions = expressionService.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, type);
        for (ZbStandardsExpression standardsExpression : zbStandardsExpressions) {
            Long nextId = SnowflakeIdUtils.getNextId();
            sysAndNewExpressionIdMap.put(standardsExpression.getId(), nextId);
            standardsExpression.setId(nextId);
            standardsExpression.setQyCode(customerCode);
            standardsExpression.setCreateTime(null);
        }
        if (CollectionUtils.isNotEmpty(zbStandardsExpressions)){
            zbStandardsExpressionMapper.batchInsert(zbStandardsExpressions);
        }
        return sysAndNewExpressionIdMap;
    }

    /**
     * @description: 给企业创建系统内置的副本-插入分类视图
     * @param projectFeature
     * @return java.util.List<com.gcj.zblib.standardData.feature.entity.ProjectFeatureCategoryView>
     * <AUTHOR>
     * @date 2021/11/11 15:49
     */
    @Override
    public List<ProjectFeatureCategoryView> initCategoryView(ProjectFeature projectFeature, Map<String, List<ProjectFeatureCategoryView>> groupMap){
        String customerCode = projectFeature.getCustomerCode();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));

        // 工程分类
        String projectType = projectFeature.getProjectType();
        projectType = CategoryUtil.projectTypeConvert(categoryListTree, projectType, BusinessConstants.PROJECT_TYPE_CONVERT_OLD);
        Set<String> categoryCodeSet = getCategoryCode(projectType);
        List<ProjectFeatureCategoryView> projectFeatureCategoryViewList = new ArrayList<>();

        for (String categoryCode : categoryCodeSet) {
            ProjectFeatureCategoryView projectFeatureCategoryView = new ProjectFeatureCategoryView();
            Long tradeId = projectFeature.getTradeId();
            projectFeatureCategoryView.setTradeId(tradeId);
            projectFeatureCategoryView.setCategoryCode(categoryCode);
            projectFeatureCategoryView.setFeatureId(projectFeature.getId());
            projectFeatureCategoryView.setCustomerCode(projectFeature.getCustomerCode());
            projectFeatureCategoryView.setType(projectFeature.getType());
            // 排序字段
            List<ProjectFeatureCategoryView> projectFeatures = Optional.ofNullable(groupMap.get(categoryCode)).orElse(new ArrayList<>());
            // 插入到最后
            Integer max = projectFeatures.stream().filter(x -> x.getTradeId().equals(tradeId)).map(ProjectFeatureCategoryView::getOrdCategory).filter(Objects::nonNull).max(Comparator.comparingInt(o -> o)).orElse(0);
            projectFeatureCategoryView.setOrdCategory(max + 1);
            projectFeatureCategoryViewList.add(projectFeatureCategoryView);
            // 放入最新的
            projectFeatures.add(projectFeatureCategoryView);
            groupMap.put(categoryCode, projectFeatures);
        }

        return projectFeatureCategoryViewList;
    }

    /**
     * @description: 查询工程特征列表(分类视图)
     * @param filterVO
     * @return java.util.List<com.gcj.zblib.standardData.feature.vo.ProjectFeatureCategoryViewVO>
     * <AUTHOR>
     * @date 2021/11/3 15:09
     */
    @Override
    //@BusinessCache(customerCode = "${customerCode}")
    public List<ProjectFeatureCategoryViewVO> featureCategoryView(String customerCode, ProjectFeatureFilterVO filterVO) {
        String categoryCode = filterVO.getCategoryCode();
        if (StringUtils.isEmpty(categoryCode)){
            throw new BusinessException(ResponseCode.PARAMETER_ERROR, "工程分类编码不能为空...");
        }

        Integer type = filterVO.getType();
        if (type == null){
            type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        }

        List<ProjectFeatureCategoryView> categoryViews = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(categoryCode, customerCode, type);

        if (CollectionUtils.isEmpty(categoryViews)) {
            initTradeService.initData(customerCode);
            categoryViews = projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(categoryCode, customerCode, type);
        }

        if (CollectionUtils.isEmpty(categoryViews)){
            return new ArrayList<>();
        }

        List<Long> featureIds = categoryViews.stream()
                .map(ProjectFeatureCategoryView::getFeatureId).collect(Collectors.toList());

        // 该分类下的特征
        List<ProjectFeature> projectFeatures = projectFeatureMapper.selectByFeatureIdList(featureIds, customerCode, filterVO.getIsShowNotUsing(), type);

        //转换工程分类json mark_projectType
        if (CollUtil.isNotEmpty(projectFeatures)){
            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
            projectFeatures.forEach(projectFeature -> projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD)));
        }

        featureCommonHandler.fillExpressionCode(projectFeatures, customerCode, type);

        return featureCommonHandler.convertToCategoryViewVO(projectFeatures, categoryViews, customerCode, filterVO.getViewType(), filterVO.getCategoryCode(), filterVO.getIsSkipUserName());
    }

    /**
     * @description: 根据工程分类获取-特征的工程专业列表
     * @param categoryCode
     * @return com.glodon.qydata.vo.standard.trade.StandardTradeVO
     * <AUTHOR>
     * @date 2022/1/17 19:27
     */
    @Override
    public List<StandardTradeVO> getTradeByCategoryFeature(String categoryCode) {
        String customerCode = RequestContent.getCustomerCode();
        // 该分类下的特征
        List<ProjectFeatureCategoryView> categoryViews =
                projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCode(categoryCode, customerCode,null);

        if (CollectionUtils.isNotEmpty(categoryViews)){
            // 对应的专业
            List<Long> tradeIds = categoryViews.stream().map(ProjectFeatureCategoryView::getTradeId).distinct().collect(Collectors.toList());

            // 过滤掉没有专业
            List<StandardTradeVO> tradeList = tradeService.getTradeVoList(customerCode);

            if (CollectionUtils.isNotEmpty(tradeList)){
                return tradeList.stream().filter(x -> tradeIds.contains(x.getId())).collect(Collectors.toList());
            }
        }

        return new ArrayList<>();
    }

    @Override
    public List<ProjectFeatureResultVO> getFeatureByCategoryAndTrade(String categoryCode, Long tradeId) {
        List<ProjectFeatureResultVO> resultVOList = new ArrayList<>();

        String customerCode = RequestContent.getCustomerCode();
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);

        List<ProjectFeatureCategoryView> projectFeatureCategoryViews =
                projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCodeAndTradeId(categoryCode, customerCode, tradeId, type);

        if (CollectionUtils.isEmpty(projectFeatureCategoryViews) && projectFeatureMapper.selectInit(customerCode, type) == 0){
            initTradeService.initData(customerCode);
            projectFeatureCategoryViews =
                    projectFeatureCategoryViewMapper.selectByCategoryAndCustomerCodeAndTradeId(categoryCode, customerCode, tradeId, type);
        }

        if (CollectionUtils.isNotEmpty(projectFeatureCategoryViews)){
            Map<Long, Integer> ordCategoryMap = projectFeatureCategoryViews.stream()
                    .collect(Collectors.toMap(ProjectFeatureCategoryView::getFeatureId, ProjectFeatureCategoryView::getOrdCategory, (v1, v2) -> v2));

            List<Long> featureIds = projectFeatureCategoryViews.stream().map(ProjectFeatureCategoryView::getFeatureId).collect(Collectors.toList());

            // 该分类下的特征
            List<ProjectFeature> projectFeatures = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(featureIds)){
                projectFeatures = projectFeatureMapper.selectByFeatureIdList(featureIds, customerCode, null, type);
            }

            //转化projectType mark_projectType
            if (CollUtil.isNotEmpty(projectFeatures)) {
                List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
                projectFeatures.forEach(projectFeature -> projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD)));
            }

            // in查询后，顺序会按主键排序，需要重新还原到分类视图下的排序
            projectFeatures.sort((o1, o2) -> {
                int io1 = featureIds.indexOf(o1.getId());
                int io2 = featureIds.indexOf(o2.getId());
                return io1 - io2;
            });

            // 填充 创建人、更新人姓名
            resultVOList = featureCommonHandler.convertToResultVO(projectFeatures, Constants.ZbFeatureConstants.ViewType.CATEGORY_VIEW, categoryCode, WHETHER_FALSE);

            for (ProjectFeatureResultVO resultVO : resultVOList) {
                resultVO.setOrdCategory(ordCategoryMap.get(resultVO.getId()));
            }
        }
        return resultVOList;
    }

    /**
     * @description: 通过Excel导入内置工程特征数据
     * @param file
     * @return void
     * <AUTHOR>
     * @date 2022/2/28 10:09
     */
    @Override
    public void importTz(MultipartFile file)  {
        // 读取Excel
        List<TzExcel> list = ExcelUtil.readExcel(file, null, TzExcel.class);

        // 获取工程专业的名称和id的映射关系
        List<ZbStandardsTrade> systemTradeList = tradeMapper.selectListByCustomerCodeAndType(Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE, Constants.ZbStandardsTradeConstants.TradeType.SYSTEM_TRADE);
        if (CollectionUtils.isEmpty(systemTradeList)){
            throw new BusinessException("未获取到内置专业工程...");
        }

        Map<String, Long> tradeMap = systemTradeList.stream().collect(Collectors.toMap(ZbStandardsTrade::getDescription, ZbStandardsTrade::getId, (v1, v2) -> v2));

        // 获取工程分类的一级分类名称和叶子节点的映射关系
        Map<String, List<List<String>>> systemCategoryMap = commonProjCategoryService.getSystemCategory();

        List<ProjectFeature> result = new ArrayList<>();

        // 按
        Long tradeId = null;
        int ordNum = 1;
        for (TzExcel tzExcel : list) {
            if (tzExcel.getNum().equals("专业")){
                String name = tzExcel.getName();
                if (!tradeMap.containsKey(name)){
                    log.error("未找到-{}-对应的专业id,tradeMap:{}", name, tradeMap);
                    return;
                }
                tradeId = tradeMap.get(name);
                ordNum = 1;
            }else {
                ProjectFeature projectFeature = new ProjectFeature();
                projectFeature.setId(SnowflakeIdUtils.getNextId());
                // 名称处理
                String name = tzExcel.getName();
                name = name.replaceAll("\\*", "");
                // 类型处理
                String typeCode = tzExcel.getTypeCode();
                typeCode = ExpressionTypeEnum.getCodeByName(typeCode);
                if (StringUtils.isEmpty(typeCode)){
                    log.error("未找到-{}-对应的类型", tzExcel.getTypeCode());
                    return;
                }
                // 枚举项处理
                String option = tzExcel.getOption();
                if (StringUtils.isNotEmpty(option)){
                    if (!typeCode.equals(ExpressionTypeEnum.TYPE_SELECT.getCode()) && !typeCode.equals(ExpressionTypeEnum.TYPE_SELECTS.getCode())){
                        log.error("{}-类型不应该有枚举值", tzExcel.getTypeCode());
                        return;
                    }
                    String[] optionSplit = option.split("\\|");
                    List<Map<String, Object>> mapList = new ArrayList<>();
                    for (String value : optionSplit) {
                        Map<String, Object> map = new LinkedHashMap<>();
                        map.put("name", value);
                        map.put("isDeleted", 0);
                        mapList.add(map);
                    }
                    option = JSONArray.toJSONString(mapList);
                }
                // 获取expressionId
                Long expressionId = addExpressionData(name, typeCode, option,
                        Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE,
                        Long.parseLong(Constants.ZbStandardsTradeConstants.SYSTEM_CUSTOMER_CODE),
                        Constants.ZbStandardsTradeConstants.CATEGORY_DEFAULT_TYPE);
                projectFeature.setExpressionId(expressionId);
                // 工程分类处理
                String projectType = tzExcel.getProjectType();
                if (StringUtils.isNotEmpty(projectType)){
                    List<List<String>> projectTypeList = new ArrayList<>();
                    String[] split = projectType.split("、");
                    for (String categoryName : split) {
                        if (!systemCategoryMap.containsKey(categoryName)){
                            log.error("未找到-{}-对应的分类,systemCategoryMap:{}", categoryName, systemCategoryMap);
                            return;
                        }
                        projectTypeList.addAll(systemCategoryMap.get(categoryName));
                    }
                    Map<String, List<List<String>>> map = new HashMap<>();
                    map.put(BusinessConstants.PROJECT_TYPE, projectTypeList);
                    projectFeature.setProjectType(JSONObject.toJSONString(map));
                }
                // 是否启用
                String isUsing = tzExcel.getIsUsing();
                if (StringUtils.isNotEmpty(isUsing) && isUsing.equals("□√")){
                    projectFeature.setIsUsing(1);
                }else {
                    projectFeature.setIsUsing(0);
                }
                // 是否默认，不明确用途
                projectFeature.setIsDefault(1);
                // 是否必填
                String isRequired = tzExcel.getIsRequired();
                if (StringUtils.isNotEmpty(isRequired) && isRequired.equals("□√")){
                    projectFeature.setIsRequired(1);
                }else {
                    projectFeature.setIsRequired(0);
                }
                // 是否计算口径
                String expression = tzExcel.getIsExpression();
                if (StringUtils.isNotEmpty(expression) && expression.equals("□√")){
                    if (!tzExcel.getTypeCode().equals("数值类")){
                        log.error("数值类型的才能成为计算口径：{}", tzExcel.getName());
                        return;
                    }
                    projectFeature.setIsExpression(1);
                }else {
                    projectFeature.setIsExpression(0);
                }
                // 排序
                projectFeature.setOrdTrade(ordNum++);
                // 工程专业id
                if (Objects.isNull(tradeId)){
                    log.error("工程专业id不能为空：{}", tzExcel.getName());
                    return;
                }
                projectFeature.setTradeId(tradeId);
                // 系统信息
                projectFeature.setCustomerCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE);
                projectFeature.setCreateGlobalId(Long.parseLong(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE));
                projectFeature.setCreateTime(null);
                projectFeature.setIsDeleted(0);
                projectFeature.setIsSync(Constants.ZbFeatureConstants.WHETHER_FALSE);
                projectFeature.setIsFromSystem(WHETHER_TRUE);
                projectFeature.setIsSearchCondition(Constants.ZbFeatureConstants.WHETHER_FALSE);
                projectFeature.setType(1);
                result.add(projectFeature);
            }
        }
        projectFeatureMapper.batchInsert(result);
    }

    private Long addExpressionData(String name, String typeCode, String option, String customCode, Long globalId, Integer type) {
        // 查询是否存在（名称+类型+枚举值）
        ZbStandardsExpression exist;
        // 单项和多选需要考虑枚举值是否相等
        if (Constants.ZbExpressionConstants.TYPE_SELECT.equals(typeCode) || Constants.ZbExpressionConstants.TYPE_SELECTS.equals(typeCode)) {
            exist = zbStandardsExpressionMapper.findExistByNameTypeOptionDs(customCode, name, typeCode, option, type);
        }else {
            exist = zbStandardsExpressionMapper.findExistByNameTypeDs(customCode, name, typeCode, type);
        }

        if (Objects.nonNull(exist)) {
            return exist.getId();
        }

        // 不存在就新增
        ZbStandardsExpression zbStandardsExpression = new ZbStandardsExpression();
        zbStandardsExpression.setId(SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setExpressionCode("K_"+SnowflakeIdUtils.getNextId());
        zbStandardsExpression.setQyCode(customCode);
        zbStandardsExpression.setName(name);
        zbStandardsExpression.setTypeCode(typeCode);
        zbStandardsExpression.setOption(option);
        zbStandardsExpression.setCreateGlobalId(globalId);
        zbStandardsExpression.setIsExpression(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsDeleted(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setIsUsable(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsFromSystem(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpression.setExpressionIsUsing(Constants.ZbFeatureConstants.WHETHER_FALSE);
        zbStandardsExpressionMapper.insertSelectiveDs(zbStandardsExpression);
        return zbStandardsExpression.getId();
    }

    /**
     　　* @description: 根据系统内置专业编码，查询所有专业对应的内置工程特征，并复制到最新专业id下
     　　* @param  customerCode 企业编码 globalId 创建人id tradeCode 引用的专业编码; tradeId 专业id
     　　* @return
     　　* @throws
     　　* <AUTHOR>
     　　* @date 2021/10/25 16:35
     　　*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void copyFeatureToNewTrade(String customerCode, Long globalId, Long tradeIdSys, Long tradeId, String tradeCode, String tradeName) {
        try{
            Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);

            // 将引用专业的当前企业发布的特征复制一份
            List<ProjectFeature> featureListSys = projectFeatureMapper.selectByTradeId(tradeIdSys, type);
            if(CollectionUtils.isEmpty(featureListSys)){
                return;
            }

            List<Long> featureNextId = SnowflakeIdUtils.getNextId(featureListSys.size());
            // 新旧工程特征id的对应关系集合，key:oldId  value:newId
            Map<Long,Long> featureIdRelationMap = new HashMap<>();
            for (int i = 0; i < featureListSys.size(); i++) {
                ProjectFeature newFeature = featureListSys.get(i);
                featureIdRelationMap.put(newFeature.getId(), featureNextId.get(i));
                newFeature.setId(featureNextId.get(i));
                newFeature.setTradeId(tradeId);
                newFeature.setTradeName(tradeName);
                newFeature.setCustomerCode(customerCode);
                newFeature.setCreateGlobalId(globalId);
                newFeature.setUpdateGlobalId(null);
                newFeature.setUpdateTime(null);
                newFeature.setIsExpressionDefault(0);
            }

//            //转换工程分类json mark_projectType
//            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_TRUE));
//            featureListSys.forEach(feature -> feature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, feature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_NEW)));

            //入库保存
            projectFeatureMapper.batchInsert(featureListSys);

            // zb_project_feature_standards
            Map<Long, Long> oldAndNewFeatureIdMap = new HashMap<>(featureListSys.size());
            List<Long> selfFeatureNextId = SnowflakeIdUtils.getNextId(featureListSys.size());
            for (int i = 0; i < featureListSys.size(); i++) {
                ProjectFeature projectFeature = featureListSys.get(i);
                Long oldId = projectFeature.getId();
                Long newId = selfFeatureNextId.get(i);
                oldAndNewFeatureIdMap.put(oldId, newId);
                projectFeature.setOriginId(oldId);
                projectFeature.setId(newId);
                projectFeature.setIsExpressionDefault(0);
            }
            projectFeatureSelfMapper.insertSelfBatch(featureListSys);

            // 处理特征数据的分类视图数据,若无数据，无需处理，直接返回
            List<ProjectFeatureCategoryView> featureCategoryViewList = projectFeatureCategoryViewMapper.selectByCategoryAndTrade(tradeIdSys,null, type);
            if(CollectionUtils.isEmpty(featureCategoryViewList)){
                return;
            }

            int sizeView = featureCategoryViewList.size();
            List<Long> featureCategoryNextId = SnowflakeIdUtils.getNextId(sizeView);
            for (int i = 0; i < sizeView; i++) {
                ProjectFeatureCategoryView view = featureCategoryViewList.get(i);
                Long oldFeatureId = view.getFeatureId();
                // 特征id无对应关系则直接跳出
                if(!featureIdRelationMap.containsKey(oldFeatureId)){
                    continue;
                }
                view.setId(featureCategoryNextId.get(i));
                view.setTradeId(tradeId);
                view.setTradeName(tradeName);
                view.setFeatureId(featureIdRelationMap.get(oldFeatureId));
                view.setCustomerCode(customerCode);
            }
            projectFeatureCategoryViewMapper.saveBatch(featureCategoryViewList);

            // zb_project_feature_category_view_standards
            List<Long> selfFeatureCategoryNextId = SnowflakeIdUtils.getNextId(featureCategoryViewList.size());
            for (int i = 0; i < featureCategoryViewList.size(); i++) {
                ProjectFeatureCategoryView featureCategoryView = featureCategoryViewList.get(i);
                featureCategoryView.setOriginId(featureCategoryView.getId());
                featureCategoryView.setId(selfFeatureCategoryNextId.get(i));
                featureCategoryView.setFeatureId(oldAndNewFeatureIdMap.get(featureCategoryView.getFeatureId()));
            }
            featureCategoryViewList = featureCategoryViewList.stream().filter(x -> x.getFeatureId() != null).collect(Collectors.toList()); //去除空ID
            projectFeatureCategoryViewSelfMapper.insertSelfBatch(featureCategoryViewList);
        }catch (Exception e){
            log.error("复制工程特征出错", e);
            throw new BusinessException(ResponseCode.ERROR,"复制工程特征出错");
        }

    }

    /**
     * 专业视图数据正常，分类视图数据为空时，同步分类视图数据
     * @throws
     * @param customerCodeTypeMap 企业编码和工程分类类型的映射
     * <AUTHOR>
     * @return
     * @date 2022/4/14 14:30
     */
    @Override
    public void syncCategoryViewFeature(Map<String, Integer> customerCodeTypeMap) {
        List<ProjectFeatureCategoryView> featureCategoryViewList = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : customerCodeTypeMap.entrySet()) {
            String customerCode = entry.getKey();
            List<ProjectFeature> projectFeatures = projectFeatureMapper.selectByCustomeCode(customerCode, entry.getValue(), null);
            for (ProjectFeature projectFeature : projectFeatures) {

                //转化projectType mark_projectType
                if (StringUtils.isNotBlank(projectFeature.getProjectType())) {
                    List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, entry.getValue(), Constants.CategoryConstants.WHETHER_FALSE));
                    projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD));
                }

                projectFeature.setCustomerCode(customerCode);
                projectFeature.setCreateTime(null);
                projectFeature.setUpdateTime(null);
                // todo 这块代码 groupMap没有初始化可能导致数据 顺序不正确
                Map<String, List<ProjectFeatureCategoryView>> groupMap = new HashMap<>(16);
                List<ProjectFeatureCategoryView> categoryViews = this.initCategoryView(projectFeature, groupMap);
                featureCategoryViewList.addAll(categoryViews);
            }
        }
        if (CollectionUtils.isNotEmpty(featureCategoryViewList)) {
            projectFeatureCategoryViewMapper.saveBatch(featureCategoryViewList);
            log.info("同步分类视图工程特征数据时共存库数据{}条,相关企业编码：{}", featureCategoryViewList.size(), customerCodeTypeMap.keySet().toString());
        }
    }

    public Map<Long, Long> initExpressionIdMap(String customerCode, Integer type, Integer newType){
        Map<Long, Long> sysAndNewExpressionIdMap = new HashMap<>();
        // 先把计算口径表复制一份
        List<ZbStandardsExpression> zbStandardsExpressions = expressionService.selectListByCustomCode(Constants.ZbFeatureConstants.SYSTEM_CUSTOMER_CODE, type);
        for (ZbStandardsExpression standardsExpression : zbStandardsExpressions) {
            Long nextId = SnowflakeIdUtils.getNextId();
            sysAndNewExpressionIdMap.put(standardsExpression.getId(), nextId);
            standardsExpression.setId(nextId);
            standardsExpression.setQyCode(customerCode);
            standardsExpression.setType(newType);
            standardsExpression.setCreateTime(null);
        }
        if (CollectionUtils.isNotEmpty(zbStandardsExpressions)){
            zbStandardsExpressionMapper.batchInsert(zbStandardsExpressions);
        }
        return sysAndNewExpressionIdMap;
    }
    
    /**
     * 通过专业id获取工程特征
     * @param customerCode
     * @param tradeIds  专业idList
     */
    @Override
    //@BusinessCache(customerCode = "${customerCode}")
    public Map<String, List<ProjectFeatureResultVO>> filterFeatureByTrades(String customerCode, List<Long> tradeIds) {
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        ProjectFeatureFilterVO filterVO = new ProjectFeatureFilterVO();
        filterVO.setType(type);
        filterVO.setViewType(Constants.ZbFeatureConstants.ViewType.TRADE_VIEW);
        filterVO.setIsSkipUserName(WHETHER_TRUE);

        // 获取每个专业的工程特征
        Map<String, List<ProjectFeatureResultVO>> resultMap = new HashMap<>();
        for (Long tradeId : tradeIds) {
            filterVO.setTradeId(tradeId);
            resultMap.put(String.valueOf(tradeId), this.featureTradeView(customerCode, filterVO));
        }
        return resultMap;
    }

    /**
     * @Description: 通过专业id集合获取工程特征, 若集合为空，则查询该企业所有专业的工程特征信息
     * @param customerCode
     * @param tradeIds
     * @return java.util.Map<java.lang.String,java.util.List<com.glodon.qydata.vo.standard.feature.ProjectFeatureResultVO>>
     * @Author: zhangj-cl
     * @Date: 2022/12/8 15:33
     */
    @Override
    public Map<String, List<ProjectFeatureResultVO>> getByTradesV2(String customerCode, List<Long> tradeIds, Integer isNeedCategoryInfo) {
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<ProjectFeatureResultVO> featureList = projectFeatureMapper.selectTradeProjectFeature(customerCode, type, tradeIds, isNeedCategoryInfo);
        if (CollectionUtils.isEmpty(featureList)){
            initTradeService.initData(customerCode);
            featureList = projectFeatureMapper.selectTradeProjectFeature(customerCode, type, tradeIds, isNeedCategoryInfo);
        }

        //转化projectType mark_projectType
        if (CollUtil.isNotEmpty(featureList)) {
            List<CommonProjCategory> categoryListTree = CategoryUtil.createProjcategoryTree(commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE));
            featureList.forEach(projectFeature -> projectFeature.setProjectType(CategoryUtil.projectTypeConvert(categoryListTree, projectFeature.getProjectType(), BusinessConstants.PROJECT_TYPE_CONVERT_OLD)));
        }

        featureCommonHandler.fillVoExpressionCode(featureList, customerCode, type);
        Map<Long, List<ProjectFeatureResultVO>> map = featureList.stream().collect(Collectors.groupingBy(ProjectFeatureResultVO::getTradeId));
        List<Long> keySets = featureList.stream().sorted(Comparator.comparing(ProjectFeatureResultVO::getTradeOrder))
                .map(ProjectFeatureResultVO::getTradeId).collect(Collectors.toList());
        Map<String, List<ProjectFeatureResultVO>> resultMap = new LinkedHashMap<>();
        for (Long tradeId : keySets) {
            resultMap.put(String.valueOf(tradeId), map.get(tradeId).stream()
                    .sorted(Comparator.comparing(ProjectFeatureResultVO::getOrdTrade)).collect(Collectors.toList()));
        }
        return resultMap;
    }

    /***
     * @description: 获取所有分类
     * @return void
     * @throws
     * <AUTHOR>
     * @date 2022/7/11 14:35
     */
    public String categoryAllCode(String customerCode, int type, List<String> topCategoryNameList){
        List<CommonProjCategory> categoryList = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);

        categoryList = rangeCode(categoryList, topCategoryNameList);

        categoryList = CategoryUtil.createProjcategoryTree(categoryList);
        List<List<String>> codeList = new ArrayList<>();
        this.getCategoryLeafCode(categoryList, codeList);
        String codeString = JSONObject.toJSONString(codeList);
        return "{\"projectType\":" + codeString + "}";
    }

    private List<CommonProjCategory> rangeCode(List<CommonProjCategory> categoryList, List<String> topCategoryNameList){
        if (CollectionUtils.isEmpty(categoryList) || CollectionUtils.isEmpty(topCategoryNameList)){
            return categoryList;
        }

        Map<String, String> topNameMap = categoryList.parallelStream()
                .filter(x -> x.getLevel() == 1)
                .collect(Collectors.toMap(CommonProjCategory::getCategoryname, CommonProjCategory::getCommonprojcategoryid));

        Set<String> topCategoryIdSet = new HashSet<>();
        for (String topCategoryName : topCategoryNameList) {
            if (topNameMap.containsKey(topCategoryName)){
                topCategoryIdSet.add(topNameMap.get(topCategoryName));
            }
        }

        return categoryList.stream().filter(x -> topCategoryIdSet.contains(x.getCategorycode1())).collect(Collectors.toList());
    }

    /***
     * @description: 递归获取所有分类的叶子节点
     * @param categoryList 1
     * @return
     * @throws
     * <AUTHOR>
     * @date 2022/7/11 14:35
     */
    private void getCategoryLeafCode(List<CommonProjCategory> categoryList, List<List<String>> codeList){
        categoryList.forEach(item -> {
            if (item.getSublist() != null && item.getSublist().size() > 0) {
                this.getCategoryLeafCode(item.getSublist(), codeList);
            } else {
                List<String> categorycodeList = new ArrayList<>();
                if (StringUtils.isNotEmpty(item.getCategorycode1())) {
                    categorycodeList.add(item.getCategorycode1());
                }
                if (StringUtils.isNotEmpty(item.getCategorycode2())) {
                    categorycodeList.add(item.getCategorycode2());
                }
                if (StringUtils.isNotEmpty(item.getCategorycode3())) {
                    categorycodeList.add(item.getCategorycode3());
                }
                if (StringUtils.isNotEmpty(item.getCategorycode4())) {
                    categorycodeList.add(item.getCategorycode4());
                }
                codeList.add(categorycodeList);
            }
        });
    }

    /**
     * projectType 转成一级的工程分类Code
     * @param projectType
     * @return
     * <AUTHOR>
     */
    @Override
    public List<String> projectTypeConvertToFirstCategoryCode(String projectType) {
        List<String> categoryCodes = new ArrayList<>();
        if(StringUtils.isBlank(projectType)) return categoryCodes;
        
        JSONObject obj = JSONObject.parseObject(projectType);
        JSONArray projectTypeArr = obj.getJSONArray(BusinessConstants.PROJECT_TYPE);
        if(CollectionUtils.isEmpty(projectTypeArr)) return categoryCodes;

        for (Object o : projectTypeArr) {
            JSONArray category = (JSONArray) o;
            String firstCategory = category.getString(0);
            if(!categoryCodes.contains(firstCategory)){
                categoryCodes.add(firstCategory);
            }
        }
        Collections.sort(categoryCodes);
        return categoryCodes;
    }


    @Override
    public void tempRefreshOrd(String customerCode) {
        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        List<CommonProjCategory> categoryListTree = commonProjCategoryService.getCategoryListTree(customerCode, type, Constants.CategoryConstants.WHETHER_FALSE);

        List<String> level1Category = categoryListTree.stream()
                .filter(x -> x.getLevel() == 1)
                .map(CommonProjCategory::getCommonprojcategoryid)
                .collect(Collectors.toList());

        ProjectFeatureFilterVO filterVO = new ProjectFeatureFilterVO();
        filterVO.setIsSkipUserName(WHETHER_TRUE);
        filterVO.setIsShowNotUsing(Constants.ZbFeatureConstants.WHETHER_TRUE);
        filterVO.setViewType(1);
        filterVO.setType(type);

        List<ProjectFeatureCategoryView> updateOrdList = new ArrayList<>();

        // 分业态
        for (String level1CategoryCode : level1Category) {
            filterVO.setCategoryCode(level1CategoryCode);

            List<ProjectFeatureCategoryViewVO> projectFeatureCategoryViewVOS = featureCategoryView(customerCode, filterVO);

            // 分专业
            for (ProjectFeatureCategoryViewVO projectFeatureCategoryViewVO : projectFeatureCategoryViewVOS) {
                Long tradeId = projectFeatureCategoryViewVO.getTradeId();
                List<ProjectFeatureCategoryView> featureList = projectFeatureCategoryViewVO.getFeatureList().stream()
                        .map(projectFeatureResultVO -> {
                            ProjectFeatureCategoryView projectFeatureCategoryView = new ProjectFeatureCategoryView();
                            projectFeatureCategoryView.setType(type);
                            projectFeatureCategoryView.setTradeId(tradeId);
                            projectFeatureCategoryView.setCategoryCode(level1CategoryCode);
                            projectFeatureCategoryView.setFeatureId(projectFeatureResultVO.getId());
                            projectFeatureCategoryView.setOrdCategory(projectFeatureResultVO.getOrdCategory());
                            projectFeatureCategoryView.setCustomerCode(customerCode);
                            return projectFeatureCategoryView;
                        })
                        .collect(Collectors.toList());

                updateOrdList.addAll(featureList);
            }
        }

        //projectFeatureCategoryViewMapper.batchUpdateOrd(updateOrdList);
        if (!CollectionUtils.isEmpty(updateOrdList)) {
            updateOrdList.forEach(x -> projectFeatureCategoryViewMapper.updateOrd(x));
        }

    }

    private Boolean isInEditing(String customerCode, Integer type) {
        Integer count = projectFeatureSelfMapper.selectRecordCount(customerCode, type);
        if (count > 0) {
            return true;
        }

        count = projectFeatureCategoryViewSelfMapper.selectRecordCount(customerCode, type);
        if (count > 0) {
            return true;
        }

        return false;
    }
    /**
     * 批量添加计算口径
     * @param customerCode
     * @param batchAddExpressionDto
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @BusinessCache(customerCode = "${customerCode}", isInvalidateCache = true)
    public void batchAddExpressions(BatchAddExpressionDto batchAddExpressionDto, String globalId, String customerCode) {
        if (StringUtils.isEmpty(batchAddExpressionDto.getCategoryCode())) {
            throw new IllegalArgumentException("工程分类编码不能为空");
        }
        if (CollUtil.isEmpty(batchAddExpressionDto.getExpressions())) {
            throw new IllegalArgumentException("无有效的口径信息");
        }

        Map<String, List<ExpressionItem>> featureOfTradeMap = new HashMap<>();
        Set<String> unitSet = new HashSet<>();
        for (ExpressionItem item : batchAddExpressionDto.getExpressions()) {
            if (StringUtils.isEmpty(item.getName()) || StringUtils.isEmpty(item.getUnit()) || CollUtil.isEmpty(item.getTradeCodeVos())) {
                throw new IllegalArgumentException("计算口径关联的名称、单位、专业不能为空");
            }
            for (String tradeName : item.getTradeCodeVos()) {
                if (!StringUtils.isEmpty(tradeName)) {
                    featureOfTradeMap.computeIfAbsent(tradeName, k -> new ArrayList<>()).add(item);
                }
            }

            unitSet.add(item.getUnit());
        }

        if (featureOfTradeMap.size() == 0) {
            throw new IllegalArgumentException("请检查是否包含有效的专业信息");
        }

        Integer type = commonProjectCategoryUsedService.getUsedCategoryType(customerCode);
        if (isInEditing(customerCode, type)) {
            throw new BusinessException(ResponseCode.EDIT_FORBIDDEN, "当前企业存在未发布的数据,请稍后添加");
        }

        // 1.收集所有特征单位,添加到字典中
        zbUnitService.batchAddUnitIfNotExist(new ArrayList<>(unitSet), globalId, customerCode);

        // 2.批量添加计算口径
        Map<String, ZbStandardsExpression> expressionMap = expressionService.batchAddExpressions(customerCode, batchAddExpressionDto.getExpressions(), type);

        // 3.特征添加到专业视图
        List<ProjectFeature> insertCategoryFeatureList = addExpressionToTradeView(
                batchAddExpressionDto.getCategoryCode(), globalId, customerCode, featureOfTradeMap, type, expressionMap);

        // 4.统一处理分类视图
        syncFeatureToCategoryView(batchAddExpressionDto.getCategoryCode(), customerCode, type, insertCategoryFeatureList);

        // 5.更新发布版本
        publishInfoServiceImpl.updateVersion(customerCode, globalId, OperateConstants.FEATURE);
    }

    /**
     * 添加特征到专业视图
     * @param categoryCode1
     * @param globalId
     * @param customerCode
     * @param featureOfTradeMap
     * @param type
     * @param expressionMap
     * @return 返回添加或修改的特征项
     */
    private List<ProjectFeature> addExpressionToTradeView(String categoryCode1, String globalId, String customerCode, Map<String, List<ExpressionItem>> featureOfTradeMap, Integer type, Map<String, ZbStandardsExpression> expressionMap) {
        // 1.查询企业下的专业
        List<ZbStandardsTrade> dbTradeList = tradeMapper.selectAllListByCustomerCode(customerCode);
        Map<String, Long>  dbTradeCodeAndIdMap = dbTradeList.stream().collect(Collectors.toMap(ZbStandardsTrade::getTradeCode, ZbStandardsTrade::getId, (v1, v2) -> v2));

        List<ProjectFeature> insertFeatureList = new ArrayList();
        List<ProjectFeature> updateFeatureList = new ArrayList();
        List<ProjectFeature> insertCategoryFeatureList = new ArrayList<>();
        // 2.依次处理每个专业
        for (Map.Entry<String, List<ExpressionItem>> entry : featureOfTradeMap.entrySet()) {
            String tradeCode = entry.getKey();
            Long tradeId = dbTradeCodeAndIdMap.get(tradeCode);
            if (tradeId == null) {
                log.warn("专业编码{}不存在,不添加特征信息", tradeCode);
                continue;
            }

            // 3. 收集专业下需要添加的特征
            List<ExpressionItem> expressionItems = entry.getValue();
            List<String> expressionNameList = expressionItems.stream().map(ExpressionItem::getName).collect(Collectors.toList());
            Map<String, ExpressionItem> expressionItemMap = expressionItems.stream().collect(Collectors.toMap(ExpressionItem::getName, Function.identity(), (v1, v2) -> v2));
            // 4. 查询专业下的最大序号
            Integer maxOrd = Optional.ofNullable(projectFeatureMapper.selectTradeFeatureMaxOrd(customerCode, type, tradeId)).orElse(0);
            // 5. 查询专业下已经存在的特征
            List<ProjectFeature> dbTradeFeatureList = projectFeatureMapper.selectTradeFeatureByCondition(customerCode, type, Arrays.asList(tradeId), expressionNameList);
            Set<Long> dbTradeFeatureIds = dbTradeFeatureList.stream().map(x->x.getId()).collect(Collectors.toSet());
            Map<String, ProjectFeature> existFeatureNameSet = dbTradeFeatureList.stream().collect(Collectors.toMap(ProjectFeature::getName, v -> v));
            // 6. 查询分类视图下已经存在的特征id
            Set<Long> dbCategoryFeatureIds = Collections.emptySet();
            if (CollUtil.isNotEmpty(dbTradeFeatureList)) {
                List<ProjectFeatureCategoryView> dbCategoryFeatureList = projectFeatureCategoryViewMapper.selectByCategoryAndFeatureIds(customerCode, categoryCode1, dbTradeFeatureIds.stream().toList());
                dbCategoryFeatureIds = dbCategoryFeatureList.stream().map(x->x.getFeatureId()).collect(Collectors.toSet());
            }

            // 7.添加专业视图特征
            for (Map.Entry<String, ExpressionItem> item : expressionItemMap.entrySet()) {
                String featureName = item.getKey();
                ZbStandardsExpression dbExpression = expressionMap.get(featureName);
                if (existFeatureNameSet.containsKey(featureName)) {
                    ProjectFeature dbFeature = existFeatureNameSet.get(featureName);
                    // 修改计算口径类型
                    dbFeature.setIsExpression(WHETHER_TRUE);
                    dbFeature.setTypeCode(dbExpression.getTypeCode());
                    dbFeature.setExpressionId(dbExpression.getId());
                    dbFeature.setExpressionCode(dbExpression.getExpressionCode());
                    dbFeature.setUnit(dbExpression.getUnit());
                    dbFeature.setIsExpressionDefault(WHETHER_FALSE);

                    // 7.1 如果分类视图下不存在当前特征id,专业视图添加工程分类
                    if (!dbCategoryFeatureIds.contains(dbFeature.getId())) {
                        dbFeature.setProjectType(featureCommonHandler.addNewCategoryCode(dbFeature.getProjectType(), categoryCode1));
                        insertCategoryFeatureList.add(dbFeature);
                    }

                    updateFeatureList.add(dbFeature);
                } else {
                    log.info("补充新的计算口径,专业:{},特征:{}", tradeCode, featureName);
                    ProjectFeature newFeature = new ProjectFeature();
                    newFeature.setId(SnowflakeIdUtils.getNextId());
                    newFeature.setOrdTrade(++maxOrd);
                    newFeature.setName(dbExpression.getName());
                    newFeature.setUnit(dbExpression.getUnit());
                    newFeature.setTypeCode(dbExpression.getTypeCode());
                    newFeature.setCustomerCode(customerCode);
                    newFeature.setType(type);
                    newFeature.setCreateGlobalId(Long.parseLong(globalId));

                    // 专业信息
                    newFeature.setTradeId(tradeId);
                    newFeature.setTradeName(tradeCode);

                    // 计算口径字段
                    newFeature.setIsUsing(dbExpression.getExpressionIsUsing());
                    newFeature.setExpressionId(dbExpression.getId());
                    newFeature.setIsExpression(WHETHER_TRUE);
                    newFeature.setExpressionCode(dbExpression.getExpressionCode());
                    newFeature.setIsExpressionDefault(WHETHER_FALSE);
                    newFeature.setIsDefault(WHETHER_FALSE);
                    newFeature.setIsDeleted(WHETHER_FALSE);

                    // 工程分类字段
                    newFeature.setProjectType(featureCommonHandler.addNewCategoryCode(null, categoryCode1));

                    insertFeatureList.add(newFeature);
                    insertCategoryFeatureList.add(newFeature);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(updateFeatureList)) {
            projectFeatureMapper.batchUpdateSelective(updateFeatureList);
        }
        if(CollectionUtils.isNotEmpty(insertFeatureList)){
            projectFeatureMapper.batchInsert(insertFeatureList);
        }
        return insertCategoryFeatureList;
    }

    /**
     * 同步特征到分类视图
     * @param categoryCode1: 一级工程分类编码
     * @param customerCode: 企业编码
     * @param type: 企业工程分类类型
     * @param featureList: 专业视图下的特征
     */
    private void syncFeatureToCategoryView(String categoryCode1, String customerCode, Integer type, List<ProjectFeature> featureList) {
        Integer maxCategoryOrd = projectFeatureCategoryViewMapper.selectMaxOrdByCondition(categoryCode1, customerCode, type);
        List<ProjectFeatureCategoryView> insertFeatureCategoryViewList = new ArrayList<>();
        for (ProjectFeature feature : featureList) {
            ProjectFeatureCategoryView categoryView = new ProjectFeatureCategoryView();
            categoryView.setId(SnowflakeIdUtils.getNextId());
            categoryView.setTradeId(feature.getTradeId());

            categoryView.setCategoryCode(categoryCode1);
            categoryView.setType(type);
            categoryView.setFeatureId(feature.getId());
            categoryView.setOrdCategory(++maxCategoryOrd);
            categoryView.setQyCodeOld(feature.getQyCodeOld());
            categoryView.setTradeName(feature.getTradeName());

            categoryView.setInvalid(feature.getInvalid());
            categoryView.setCustomerCode(customerCode);
            categoryView.setQyFlag(feature.getQyFlag());

            insertFeatureCategoryViewList.add(categoryView);
        }

        if (!CollUtil.isEmpty(insertFeatureCategoryViewList)) {
            projectFeatureCategoryViewMapper.saveBatch(insertFeatureCategoryViewList);
        }
    }
}
