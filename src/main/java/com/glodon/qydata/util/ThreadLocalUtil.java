package com.glodon.qydata.util;

import java.lang.annotation.Annotation;
import java.lang.reflect.Method;

/**
 * @description: ThreadLocal工具类
 * <AUTHOR>
 * @date: 2022/11/9 16:12
 */
public class ThreadLocalUtil {
    private static ThreadLocal<Object> threadLocal = new ThreadLocal<>();
    private static ThreadLocal<Method> currentMethodThreadLocal = new ThreadLocal<>();

    public static Object getThreadLocal() {
        return threadLocal.get();
    }

    public static void setThreadLocal(Object object) {
        threadLocal.set(object);
    }

    public static void removeData() {
        if (threadLocal.get() != null) {
            threadLocal.remove();
        }
        if (currentMethodThreadLocal.get() != null) {
            currentMethodThreadLocal.remove();
        }
    }

    /**
     * 设置当前执行的方法
     * @param method 当前方法
     */
    public static void setCurrentMethod(Method method) {
        currentMethodThreadLocal.set(method);
    }

    /**
     * 获取当前执行的方法
     * @return 当前方法
     */
    public static Method getCurrentMethod() {
        return currentMethodThreadLocal.get();
    }

    /**
     * 获取当前方法的指定注解
     * @param annotationClass 注解类型
     * @param <T> 注解类型
     * @return 注解实例，如果不存在则返回null
     */
    public static <T extends Annotation> T getCurrentMethodAnnotation(Class<T> annotationClass) {
        Method method = getCurrentMethod();
        if (method != null) {
            return method.getAnnotation(annotationClass);
        }
        return null;
    }

    /**
     * 获取当前方法的所有注解
     * @return 注解数组
     */
    public static Annotation[] getCurrentMethodAnnotations() {
        Method method = getCurrentMethod();
        if (method != null) {
            return method.getAnnotations();
        }
        return new Annotation[0];
    }
}
