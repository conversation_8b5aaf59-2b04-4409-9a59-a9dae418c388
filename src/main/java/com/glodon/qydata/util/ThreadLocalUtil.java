package com.glodon.qydata.util;


/**
 * @description: ThreadLocal工具类
 * <AUTHOR>
 * @date: 2022/11/9 16:12
 */
public class ThreadLocalUtil {
    private static ThreadLocal<Object> threadLocal = new ThreadLocal<>();

    public static Object getThreadLocal() {
        return threadLocal.get();
    }

    public static void setThreadLocal(Object object) {
        threadLocal.set(object);
    }

   public static void removeData() {
        if (threadLocal.get() != null) {
            threadLocal.remove();
        }
   }
}
